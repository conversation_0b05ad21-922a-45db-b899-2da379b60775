import os


import pandas as pd
from keras.preprocessing.sequence import TimeseriesGenerator
import time
import numpy as np


from keras.models import Sequential
from keras.layers import LSTM
import tensorflow as tf
from sklearn.metrics import mean_squared_error
from sklearn.metrics import mean_absolute_error
from sklearn.metrics import r2_score
from tensorflow.keras.optimizers import Adam # type: ignore
from tensorflow.keras.losses import MeanAbsoluteError, MeanSquaredError # type: ignore

print(tf.config.list_physical_devices('GPU'))

# #使用GPU来训练
gpus = tf.config.list_physical_devices('GPU')
cpu = tf.config.list_physical_devices('CPU')
print(cpu)
config = tf.compat.v1.ConfigProto(
    device_count={"CPU": 16},
    inter_op_parallelism_threads=1,
    intra_op_parallelism_threads=1,
)
sess = tf.compat.v1.Session(config=config)
# if gpus:
#     print('start gpu!')
#     tf.config.list_physical_devices('GPU')
#     tf.config.experimental.set_memory_growth(gpus[0], True)
#     tf.config.set_visible_devices(gpus[0], 'GPU')



#definition of constant
lentrain = 48  # 适应96条记录的数据量，使用一半作为训练数据
testID = [1, 2, 3, 4]  # 使用新数据集中存在的基站ID
# testID = [1684]
time_step = 3
batch_size = 128
epochs = 150



def create_dynamic_lstm(layers, model):
    fList = [x for x in layers if x!= 0]
    k = len(fList)
    if k == 0:
        return model
    assert k <= 10 and k >= 1, "Number of layers should be between 1 and 10"
    total_neurons = sum(fList)
    assert total_neurons >= 1, "Total number of neurons should be greater than 1"

    # lstm_model = DynamicLSTM(input_size=x[0], hidden_size=x[1])
    for i in range(k - 1):
        model.add(LSTM(fList[i], activation='relu', return_sequences=True))

    return model

def LSTMm_bl(generator, generator_test, n_features=1, time_step=3, epochs=150, layer=[5]):
    gpus = tf.config.list_physical_devices('GPU')
    if gpus:
        print('Using GPU!')
        tf.config.experimental.set_memory_growth(gpus[0], True)
        tf.config.set_visible_devices(gpus[0], 'GPU')

    tf.random.set_seed(2)
    model = Sequential()
    model.add(LSTM(5, activation='tanh', input_shape=(time_step, n_features), return_sequences=True))
    model = create_dynamic_lstm(layer, model)
    model.add(LSTM(1, activation='tanh'))
    
    optimizer = Adam()
    loss = MeanAbsoluteError()
    metrics = [MeanSquaredError()]

    model.compile(optimizer=optimizer, loss=loss, metrics=metrics)

    model.fit(generator, epochs=epochs, shuffle=False, verbose=0, validation_data=generator_test)
    wt1 = model.get_weights()

    return model, wt1

def data_preprocess():
    # 使用新的数据集 C2TM.csv
    filename2 = r'./data/C2TM.csv'
    data3 = pd.read_csv(filename2)

    # 重命名列以匹配原有逻辑：bs -> sqID, traffic_std -> traffic
    data3 = data3.rename(columns={'bs': 'sqID'})

    # 选择需要的列，移除wkday，使用hour_bin作为hour，使用traffic_std作为流量数据
    order = ['sqID','hour_bin','traffic_std']
    data3 = data3[order]
    data3 = data3.rename(columns={'hour_bin': 'hour', 'traffic_std': 'traffic'})

    # 处理缺失值
    data3['traffic'] = data3['traffic'].fillna(0)

    data4 = data3.copy()
    data4['traffic'] = data3['traffic']

    return data4

def dynamic_LSTM(layers, id):
    #get data
    data = data_preprocess()
    #get test group 
    for sqID in [testID[id]]:
        ct2 = data[data.sqID==sqID].copy()
                
        ct1 = ct2.drop(['sqID'],axis=1)

        ct1.reset_index(drop=True, inplace=True)

        # 适应96条记录的数据分割：前48条训练，24条验证，24条测试
        total_records = len(ct1)
        train_end = lentrain  # 48
        validate_end = train_end + 24  # 72
        test_end = total_records  # 96

        train_X = ct1.values[0:train_end]
        validate_X = ct1.values[train_end:validate_end]
        test_X = ct1.values[validate_end:test_end]

        train_y = ct1['traffic'].values[0:train_end]
        validate_y = ct1['traffic'].values[train_end:validate_end]
        test_y = ct1['traffic'].values[validate_end:test_end]
        
        generator_train = TimeseriesGenerator(train_X, train_y, length=time_step, batch_size=batch_size, shuffle=False)
        generator_test = TimeseriesGenerator(test_X, test_y, length=time_step, batch_size=batch_size, shuffle=False)
        generator_validate = TimeseriesGenerator(validate_X, validate_y, length=time_step, batch_size=batch_size, shuffle=False)

        # validate = TimeseriesGenerator(validate_x, validate_y, length=time_step, batch_size=batch_size, shuffle=False)

        start1 =  time.perf_counter() 
        blmodel, wt = LSTMm_bl(generator_train, generator_test, n_features =2, time_step =time_step, epochs = epochs, layer = layers)
        elapsed1 = ( time.perf_counter() - start1)
        print("Time used (BL updating):",elapsed1)

        #predict test
        bl_y_hat = blmodel.predict(generator_test)
        bl_y_true = generator_test.targets[time_step:]
        RMSE_bl = np.sqrt(mean_squared_error(bl_y_hat,bl_y_true))
        R2_bl = r2_score(bl_y_hat,bl_y_true)

        MAE_bl = mean_absolute_error(bl_y_hat, bl_y_true)


        return RMSE_bl, R2_bl, MAE_bl, elapsed1




if __name__ == '__main__':
    print(dynamic_LSTM([2, 2, 1, 0, 0], 0))
