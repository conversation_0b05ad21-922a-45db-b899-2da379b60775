import torch
from torch import nn
import torch.nn.functional as F
import numpy as np
import collections
import random
import os


# --------------------------------------- #
# 经验回放池
# --------------------------------------- #

class ReplayBuffer:
    def __init__(self, capacity):
        # 创建一个先进先出的队列，最大长度为capacity，保证经验池的样本量不变
        self.buffer = collections.deque(maxlen=capacity)

    # 将数据以元组形式添加进经验池
    def add(self, state, action, reward, next_state, done):
        self.buffer.append((state, action, reward, next_state, done))

    # 随机采样batch_size行数据
    def sample(self, batch_size):
        transitions = random.sample(self.buffer, batch_size)
        # *transitions代表取出列表中的值，即32项
        state, action, reward, next_state, done = zip(*transitions)
        return np.array(state), action, reward, np.array(next_state), done

    # 目前队列长度
    def size(self):
        return len(self.buffer)


# -------------------------------------- #
# 构造深度学习网络，输入状态s，得到各个动作的reward
# -------------------------------------- #

class Net(nn.Module):
    def __init__(self, n_states, n_hidden, n_actions):
        super(Net, self).__init__()
        # 第一个隐藏层
        self.fc1 = nn.Linear(n_states, n_hidden)
        # 第二个隐藏层
        self.fc2 = nn.Linear(n_hidden, n_hidden)
        # 第三个隐藏层
        self.fc3 = nn.Linear(n_hidden, n_hidden)
        # 第四个隐藏层
        self.fc4 = nn.Linear(n_hidden, n_hidden)
        # 输出层
        self.fc5 = nn.Linear(n_hidden, n_actions)

    def forward(self, x):
        # 通过第一个隐藏层后应用ReLU激活函数
        x = F.relu(self.fc1(x))
        # 通过第二个隐藏层后再次应用ReLU激活函数
        x = F.relu(self.fc2(x))
        # 通过第三个隐藏层后再次应用ReLU激活函数
        x = F.relu(self.fc3(x))
        # 通过第四个隐藏层后再次应用ReLU激活函数
        x = F.relu(self.fc4(x))
        # 通过输出层，得到动作的预测值
        x = self.fc5(x)
        return x


# -------------------------------------- #
# 构造深度强化学习模型
# -------------------------------------- #

class DQN:
    #  初始化
    def __init__(self, n_states, n_hidden, n_actions,
                 learning_rate, gamma, target_update,
                 device, epsilon, N_RANGE, flag, id):
        # 属性分配
        self.n_states = n_states  # 状态的特征数
        self.n_hidden = n_hidden  # 隐含层个数
        self.n_actions = n_actions  # 动作数
        self.learning_rate = learning_rate  # 训练时的学习率
        self.gamma = gamma  # 折扣因子，对下一个状态的回报的缩放
        self.target_update = target_update  # 目标网络的参数的更新频率
        self.device = device  # 在GPU计算
        self.epsilon = epsilon  # 贪婪递减值
        self.N_RANGE = N_RANGE
        # 计数器，记录迭代次数
        self.count = 0
        self.count_ = 0
        # True为训练模式，False为预测模式
        self.flag = flag

        self.id = id

        # 模型保存路径
        self.model_path = r'D:\pythonproject\DNQ-LSTM\model'

        # 构建2个神经网络，相同的结构，不同的参数
        # 实例化训练网络  [b,4]-->[b,2]  输出动作对应的奖励
        self.q_net = Net(self.N_RANGE, self.n_hidden, self.n_actions).to(self.device)
        # 实例化目标网络
        self.target_q_net = Net(self.N_RANGE, self.n_hidden, self.n_actions).to(self.device)

        if not self.flag:
            original_dir = os.getcwd()
            print("当前工作目录是：", original_dir)
            os.chdir(self.model_path)
            q_net_point = torch.load('q_net_0.01_again.pth')
            self.q_net.load_state_dict(q_net_point['model'])
            self.target_q_net.load_state_dict(q_net_point['model'])
            os.chdir(original_dir)
            print("返回到原始工作目录:", os.getcwd())

        # 优化器，更新训练网络的参数
        self.optimizer = torch.optim.Adam(self.q_net.parameters(), lr=self.learning_rate)

    #  动作选择
    def take_action(self, state, played_count):
        state = np.array(state)
        # 维度扩充，给行增加一个维度，并转换为张量shape=[1,4]
        state = torch.Tensor(state[np.newaxis, :]).to(self.device)
        # 如果小于该值就取最大的值对应的索引     递减贪婪
        if np.random.random() >= self.epsilon ** played_count:  # 0-1
            print("+++++++++++++++++++++++++++++++++++++++++++old man+++++++++++++++++++++++++++++++++++++++++++")
            # 前向传播获取该状态对应的动作的reward
            actions_value = self.q_net(state)
            # 获取reward最大值对应的动作索引
            action = actions_value.argmax().item()  # 从 PyTorch 的张量类型转换为 Python 整数类型
        # 如果大于该值就随机探索
        else:
            # 随机选择一个动作
            print("+++++++++++++++++++++++++++++++++++++++++++random++++++++++++++++++++++++++++++++++++++++++++++")
            action = np.random.randint(self.n_actions)
        return action

    #  网络训练
    def update(self, transition_dict):  # 传入经验池中的batch个样本
        # 获取当前时刻的状态 array_shape=[b,4]
        states = torch.tensor(transition_dict['states'], dtype=torch.float).to(self.device)
        # 获取当前时刻采取的动作 tuple_shape=[b]，维度扩充 [b,1]
        actions = torch.tensor(transition_dict['actions']).view(-1, 1).to(self.device)
        # 当前状态下采取动作后得到的奖励 tuple=[b]，维度扩充 [b,1]
        rewards = torch.tensor(transition_dict['rewards'], dtype=torch.float).view(-1, 1).to(self.device)
        # 下一时刻的状态 array_shape=[b,4]
        next_states = torch.tensor(transition_dict['next_states'], dtype=torch.float).to(self.device)
        # 是否到达目标 tuple_shape=[b]，维度变换[b,1]
        dones = torch.tensor(transition_dict['dones'], dtype=torch.float).view(-1, 1).to(self.device)

        # 输入当前状态，得到采取各运动得到的奖励 [b,4]==>[b,2]==>[b,1]
        # 根据actions索引在训练网络的输出的第1维度上获取对应索引的q值（state_value）
        q_values = self.q_net(states).gather(1, actions)  # [b,1]   Q现实
        # 下一时刻的状态[b,4]-->目标网络输出下一时刻对应的动作q值[b,2]-->
        # 选出下个状态采取的动作中最大的q值[b]-->维度调整[b,1]
        max_next_q_values = self.target_q_net(next_states).max(1)[0].view(-1, 1)  # Q估计
        # 目标网络输出的当前状态的q(state_value)：即时奖励+折扣因子*下个时刻的最大回报
        q_targets = rewards + self.gamma * max_next_q_values * (1 - dones)  # 目标Q值

        # 目标网络和训练网络之间的均方误差损失
        dqn_loss = torch.mean(F.mse_loss(q_values, q_targets))
        # PyTorch中默认梯度会累积,这里需要显式将梯度置为0
        self.optimizer.zero_grad()
        # 反向传播参数更新主网络
        dqn_loss.backward()
        # 对训练网络更新
        self.optimizer.step()

        # 在一段时间后更新目标网络的参数
        if self.count % self.target_update == 0:
            self.count_ += 1
            print('+++update+++')
            # 将目标网络的参数替换成训练网络的参数
            self.target_q_net.load_state_dict(
                self.q_net.state_dict())
            original_dir = os.getcwd()
            print("原始工作目录:", original_dir)
            os.chdir(self.model_path)
            print("切换到新目录:", os.getcwd())
            torch.save({'model': self.q_net.state_dict()}, f'q_net_{self.id}_{self.count_}.pth')
            os.chdir(original_dir)
            print("返回到原始工作目录:", os.getcwd())

        self.count += 1
