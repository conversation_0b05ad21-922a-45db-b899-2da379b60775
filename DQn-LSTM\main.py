import time
import xlwt
import torch
import numpy as np
import pandas as pd
import random
import platform

from LSTM import dynamic_LSTM
from DQN import DQN, ReplayBuffer

# 随机种子设置
np.random.seed(2)

# GPU
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"using device: {device}")



# 三层网络用来生成对应的网络结果
def generate_list(low, high):
    x = y = z = low
    result = []
    while x <= high:
        y = low
        while y <= high:
            z = low
            while z <= high:
                result.append([x, y, z])
                z += 1
            y += 1
        x += 1

    return result


# 超参数
N_RANGE = 6  # 每层神经元数量（0-3）
A_RANGE = 3  # 动作
capacity = 5000  # 经验池容量
n_states = N_RANGE ** 3  # 总可能状态
n_actions = (A_RANGE * 2 + 1) ** 3  # 总可能动作
ACTIONS = [i for i in range(0, n_actions)]  # 探索者的可用动作
lr = 0.01  # 学习率
gamma = 0.8  # 折扣因子
target_update = 150  # 目标网络的参数的更新频率
batch_size = 128  # 每次送入训练的数据量
n_hidden = 256  # 隐含层神经元个数
min_size = 200  # 经验池超过200后再训练
return_list = []  # 记录每个回合的回报
MAX_EPISODES = 50  # 最大回合
epsilon = 0.99  # 贪婪递减值

S_LIST = generate_list(0, N_RANGE - 1)  # 建立状态列表
A_LIST = generate_list(-A_RANGE, A_RANGE)  # 建立动作列表
# CON_LEARN = False  # 继续学习之前的table还是从零开始
TYPE_CODE = False  # True为训练模式，False为预测模式


def init_dqn(ID):
    # 实例化经验池
    replay_buffer = ReplayBuffer(capacity)
    # 实例化DQN
    agent = DQN(n_states=n_states,
                n_hidden=n_hidden,
                n_actions=n_actions,
                learning_rate=lr,
                gamma=gamma,
                target_update=target_update,
                device=device,
                epsilon=epsilon,
                N_RANGE=3,
                flag=TYPE_CODE,
                id=ID
                )
    return replay_buffer, agent


#  环境反馈
def get_env_feedback(S, A, id):
    S_value = [x + y for x, y in zip(A_LIST[A], S_LIST[S])]  # 动作相加状态，动作下一步的状态
    # 越界判断
    terminal = False
    x = any(num < 0 for num in S_value)
    y = any(num >= N_RANGE for num in S_value)
    if x or y:
        R = -400
        terminal = True
        return S, R, terminal
    print('enter network', 'S_value=', S_value)
    RMSE_bl, R2_bl, MAE_bl, time_bl = dynamic_LSTM(S_value, id)
    print('RMSE (function LSTMmt_bl): ', RMSE_bl)
    print('R2 (function LSTMmt_bl): ', R2_bl)
    print('MAE (function LSTMmt_bl): ', MAE_bl)
    S_ = S_LIST.index(S_value)
    R2_bl = R2_bl if R2_bl > 0 else -2.5
    R = 400 * (1 - 2 * (RMSE_bl + MAE_bl)) + 80 * R2_bl

    return S_, R, terminal


def rl_instantR():
    for id in range(2, 4):  # id是不同基站
        format_time = time.strftime("%Y%m%d%H%M%S", time.localtime())
        # 添加回合表格
        round_data_workbook = xlwt.Workbook()
        # 添加奖励表格
        reward_workbook = xlwt.Workbook()
        sheet = reward_workbook.add_sheet('ep_reward_list')
        sheet.write(0, 0, 'Episode')
        sheet.write(0, 1, 'Reward')
        played_count = 0
        # 初始DQN
        replay_buffer, agent = init_dqn(id)

        S = 0  # 回合初始位置

        for episode in range(MAX_EPISODES):  # 回合
            step_counter = 0
            is_terminated = False  # 是否回合结束
            # update_env(S, episode, step_counter)  # 环境更新
            total_reward = 0
            round_sheet = round_data_workbook.add_sheet('episode' + str(episode))
            round_sheet.write(0, 0, 'Round')
            round_sheet.write(0, 1, 'Reward')
            round_sheet.write(0, 2, 'State')
            round_sheet.write(0, 3, 'Action')

            while not is_terminated:

                A = agent.take_action(S_LIST[S], played_count)  # 选行为

                S_, R, out_range = get_env_feedback(S, A, id)  # 实施行为并得到环境的反馈
                replay_buffer.add(S_LIST[S], A, R, S_LIST[S_], out_range)  # 添加经验池
                if replay_buffer.size() > min_size:
                    # 从经验池中随机抽样作为训练集
                    s1, a1, r1, ns1, d1 = replay_buffer.sample(batch_size)
                    # 构造训练集
                    transition_dict = {
                        'states': s1,
                        'actions': a1,
                        'next_states': ns1,
                        'rewards': r1,
                        'dones': d1,
                    }
                    # 网络更新
                    agent.update(transition_dict)

                step_counter += 1
                played_count += 1

                total_reward += R

                round_sheet.write(step_counter + 1, 0, str(step_counter))
                round_sheet.write(step_counter + 1, 1, str(R))
                round_sheet.write(step_counter + 1, 2, str(S_LIST[S]))
                round_sheet.write(step_counter + 1, 3, str(A_LIST[A]))

                if step_counter >= 30:  # 回合结束
                    print('round ended')
                    is_terminated = True

                S = S_  # 探索者移动到下一个 state

            # 写入奖励表格
            sheet.write(episode + 1, 0, 'episode' + str(episode))
            sheet.write(episode + 1, 1, total_reward)

            reward_workbook.save('/plot/reward_data' + '-' + format_time + '.xls')
            round_data_workbook.save('/plot/round_data' + '-' + format_time + '.xls')

            print("write excel successfully!")

    return 0


if __name__ == "__main__":
    network = []
    rl_instantR()
