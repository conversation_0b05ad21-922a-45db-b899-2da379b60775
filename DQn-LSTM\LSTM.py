import os


import pandas as pd
from keras.preprocessing.sequence import TimeseriesGenerator
import time
import numpy as np


from keras.models import Sequential
from keras.layers import LSTM
import tensorflow as tf
from sklearn.metrics import mean_squared_error
from sklearn.metrics import mean_absolute_error
from sklearn.metrics import r2_score
from tensorflow.keras.optimizers import Adam # type: ignore
from tensorflow.keras.losses import MeanAbsoluteError, MeanSquaredError # type: ignore

print(tf.config.list_physical_devices('GPU'))

# #使用GPU来训练
gpus = tf.config.list_physical_devices('GPU')
cpu = tf.config.list_physical_devices('CPU')
print(cpu)
config = tf.compat.v1.ConfigProto(
    device_count={"CPU": 16},
    inter_op_parallelism_threads=1,
    intra_op_parallelism_threads=1,
)
sess = tf.compat.v1.Session(config=config)
# if gpus:
#     print('start gpu!')
#     tf.config.list_physical_devices('GPU')
#     tf.config.experimental.set_memory_growth(gpus[0], True)
#     tf.config.set_visible_devices(gpus[0], 'GPU')



#definition of constant
lentrain = 168*5
testID = [5361, 5873, 1684, 7121]
# testID = [1684]
time_step = 3
batch_size = 128
epochs = 150



def create_dynamic_lstm(layers, model):
    fList = [x for x in layers if x!= 0]
    k = len(fList)
    if k == 0:
        return model
    assert k <= 10 and k >= 1, "Number of layers should be between 1 and 10"
    total_neurons = sum(fList)
    assert total_neurons >= 1, "Total number of neurons should be greater than 1"

    # lstm_model = DynamicLSTM(input_size=x[0], hidden_size=x[1])
    for i in range(k - 1):
        model.add(LSTM(fList[i], activation='relu', return_sequences=True))

    return model

def LSTMm_bl(generator, generator_test, n_features=1, time_step=3, epochs=150, layer=[5]):
    gpus = tf.config.list_physical_devices('GPU')
    if gpus:
        print('Using GPU!')
        tf.config.experimental.set_memory_growth(gpus[0], True)
        tf.config.set_visible_devices(gpus[0], 'GPU')

    tf.random.set_seed(2)
    model = Sequential()
    model.add(LSTM(5, activation='tanh', input_shape=(time_step, n_features), return_sequences=True))
    model = create_dynamic_lstm(layer, model)
    model.add(LSTM(1, activation='tanh'))
    
    optimizer = Adam()
    loss = MeanAbsoluteError()
    metrics = [MeanSquaredError()]

    model.compile(optimizer=optimizer, loss=loss, metrics=metrics)

    model.fit(generator, epochs=epochs, shuffle=False, verbose=0, validation_data=generator_test)
    wt1 = model.get_weights()

    return model, wt1

def data_preprocess():
    import platform

    sys_platform = platform.platform().lower()
    if "windows" in sys_platform:
        print("Windows")
        os.chdir("D:\data")
    elif "macos" in sys_platform:
        print("Mac os")
        os.chdir("/Volumes/ROG/工程/数据分析/数据集")
    elif "linux" in sys_platform:
        print("Linux")
    else:
        print("其他系统")

    filename2 = r'preprocessed_data_mid7wks_indiv_standardlized.csv'
    data3 = pd.read_csv(filename2)
    order = ['sqID','hour','wkday','traffic']
    data3 = data3[order]
    data3['traffic'] = data3['traffic'].fillna(0)

    data4 = data3.copy()
    data4['traffic'] = data3['traffic']

    return data4

def dynamic_LSTM(layers, id):
    #get data
    data = data_preprocess()
    #get test group 
    for sqID in [testID[id]]:
        ct2 = data[data.sqID==sqID].copy()
                
        ct1 = ct2.drop(['sqID'],axis=1)

        ct1.reset_index(drop=True, inplace=True)

        # train_X, test_X, validate_x, train_y, test_y, validate_y = ct1.values[0:lentrain],ct1.values[168:840+168],ct1.values[840+168*2:840+168*4],ct1['traffic'].values[lentrain:lentrain+168*2],ct1['traffic'].values[840+168:840+168*2], ct1['traffic'].values[840+168*2:840+168*4]
        train_X, validate_X, test_X, train_y, validate_y, test_y = ct1.values[840:840+168*1], ct1.values[840+168*1:840+168*2],ct1['traffic'].values[0:lentrain],ct1['traffic'].values[840:840+168*1], ct1['traffic'].values[840+168*1:840+168*2]
        
        generator_train = TimeseriesGenerator(train_X, train_y, length=time_step, batch_size=batch_size, shuffle=False)
        generator_test = TimeseriesGenerator(test_X, test_y, length=time_step, batch_size=batch_size, shuffle=False)
        generator_validate = TimeseriesGenerator(validate_X, validate_y, length=time_step, batch_size=batch_size, shuffle=False)

        # validate = TimeseriesGenerator(validate_x, validate_y, length=time_step, batch_size=batch_size, shuffle=False)

        start1 =  time.perf_counter() 
        blmodel, wt = LSTMm_bl(generator_train, generator_test, n_features =3, time_step =time_step, epochs = epochs, layer = layers)
        elapsed1 = ( time.perf_counter() - start1)
        print("Time used (BL updating):",elapsed1)

        #predict test
        bl_y_hat = blmodel.predict(generator_test)
        bl_y_true = generator_test.targets[time_step:]
        RMSE_bl = np.sqrt(mean_squared_error(bl_y_hat,bl_y_true))
        R2_bl = r2_score(bl_y_hat,bl_y_true)

        MAE_bl = mean_absolute_error(bl_y_hat, bl_y_true)


        return RMSE_bl, R2_bl, MAE_bl, elapsed1




if __name__ == '__main__':
    print(dynamic_LSTM([0, 2, 1, 0, 0], 0))
